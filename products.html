<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Products - Luxe Fashion</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'primary': '#4B0082',
                        'secondary': '#D8BFD8',
                        'accent': '#9370DB',
                        'purple-light': '#E6E6FA',
                        'purple-dark': '#301934'
                    },
                    fontFamily: {
                        'display': ['Inter', 'system-ui', 'sans-serif'],
                        'body': ['Inter', 'system-ui', 'sans-serif']
                    },
                    animation: {
                        'float': 'float 6s ease-in-out infinite',
                        'slide-up': 'slideUp 0.5s ease-out',
                        'fade-in-up': 'fadeInUp 0.6s ease-out',
                        'pulse-slow': 'pulse 3s infinite',
                        'bounce-slow': 'bounce 2s infinite'
                    },
                    backdropBlur: {
                        'xs': '2px',
                    }
                }
            }
        }
    </script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
</head>
<body class="bg-white text-gray-800 font-body">
    <!-- Navigation -->
    <nav class="bg-white/90 backdrop-blur-xl shadow-2xl fixed top-4 left-4 right-4 z-50 transition-all duration-300 rounded-2xl border border-white/20" id="navbar">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex-shrink-0">
                    <a href="index.html" class="text-2xl font-bold text-primary font-display tracking-tight">Luxe Fashion</a>
                </div>

                <!-- Desktop Menu -->
                <div class="hidden lg:block">
                    <div class="ml-10 flex items-baseline space-x-8">
                        <a href="index.html" class="text-gray-700 hover:text-primary transition duration-300 font-medium relative group">
                            Home
                            <span class="absolute -bottom-1 left-0 w-0 h-0.5 bg-primary transition-all duration-300 group-hover:w-full"></span>
                        </a>
                        <div class="relative group">
                            <a href="#" class="text-gray-700 hover:text-primary transition duration-300 font-medium flex items-center">
                                Women <i class="fas fa-chevron-down ml-1 text-xs"></i>
                            </a>
                        </div>
                        <div class="relative group">
                            <a href="#" class="text-gray-700 hover:text-primary transition duration-300 font-medium flex items-center">
                                Men <i class="fas fa-chevron-down ml-1 text-xs"></i>
                            </a>
                        </div>

                        <a href="#" class="text-red-500 hover:text-red-600 transition duration-300 font-medium relative group">
                            Sale
                            <span class="absolute -bottom-1 left-0 w-0 h-0.5 bg-red-500 transition-all duration-300 group-hover:w-full"></span>
                        </a>
                    </div>
                </div>

                <!-- Search Bar -->
                <div class="hidden md:flex flex-1 max-w-md mx-8">
                    <div class="relative w-full">
                        <input type="text" placeholder="Search for products..."
                               class="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-full focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-300"
                               id="search-input">
                        <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                    </div>
                </div>

                <!-- Icons -->
                <div class="flex items-center space-x-4">
                    <div class="relative">
                        <button class="text-gray-700 hover:text-primary cursor-pointer transition duration-300 relative" id="wishlist-btn">
                            <i class="fas fa-heart"></i>
                            <span class="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center" id="wishlist-count">0</span>
                        </button>
                        <!-- Wishlist Dropdown -->
                        <div id="wishlist-dropdown" class="absolute top-full right-0 mt-2 w-80 bg-white/95 backdrop-blur-xl rounded-2xl shadow-xl opacity-0 invisible transition-all duration-300 z-50 border border-white/20">
                            <div class="p-4">
                                <h3 class="font-semibold text-gray-800 mb-3">Wishlist</h3>
                                <div id="wishlist-items" class="space-y-3 mb-4">
                                    <div class="text-center text-gray-500 py-8">Your wishlist is empty</div>
                                </div>
                                <div class="border-t pt-3">
                                    <div class="flex justify-between items-center mb-3">
                                        <span class="font-semibold">Items:</span>
                                        <span class="font-bold text-primary" id="wishlist-item-count">0</span>
                                    </div>
                                    <button class="w-full bg-red-500 text-white py-2 rounded-lg hover:bg-red-600 transition duration-300">
                                        View All Wishlist
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="relative">
                        <button class="text-gray-700 hover:text-primary cursor-pointer transition duration-300 relative" id="cart-btn">
                            <i class="fas fa-shopping-bag"></i>
                            <span class="absolute -top-2 -right-2 bg-primary text-white text-xs rounded-full h-5 w-5 flex items-center justify-center" id="cart-count">0</span>
                        </button>
                        <!-- Cart Dropdown -->
                        <div id="cart-dropdown" class="absolute top-full right-0 mt-2 w-80 bg-white/95 backdrop-blur-xl rounded-2xl shadow-xl opacity-0 invisible transition-all duration-300 z-50 border border-white/20">
                            <div class="p-4">
                                <h3 class="font-semibold text-gray-800 mb-3">Shopping Cart</h3>
                                <div id="cart-items" class="space-y-3 mb-4">
                                    <div class="text-center text-gray-500 py-8">Your cart is empty</div>
                                </div>
                                <div class="border-t pt-3">
                                    <div class="flex justify-between items-center mb-3">
                                        <span class="font-semibold">Total:</span>
                                        <span class="font-bold text-primary" id="cart-total">$0.00</span>
                                    </div>
                                    <a href="checkout.html" class="block w-full bg-primary text-white py-2 rounded-lg hover:bg-accent transition duration-300 text-center">
                                        Checkout
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Auth Buttons -->
                    <div class="hidden lg:flex items-center space-x-3">
                        <button onclick="openLoginModal()" class="text-gray-700 hover:text-primary font-medium transition duration-300">
                            Login
                        </button>
                        <button onclick="openSignupModal()" class="bg-primary text-white px-4 py-2 rounded-full font-medium hover:bg-accent transition duration-300">
                            Sign Up
                        </button>
                    </div>

                    <!-- Mobile menu button -->
                    <button id="mobile-menu-btn" class="lg:hidden text-gray-700 hover:text-primary">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile Menu -->
        <div id="mobile-menu" class="lg:hidden hidden bg-white/95 backdrop-blur-xl border-t border-white/20 rounded-b-2xl">
            <div class="px-4 pt-4 pb-6 space-y-3">
                <!-- Mobile Search -->
                <div class="md:hidden mb-4">
                    <div class="relative">
                        <input type="text" placeholder="Search products..."
                               class="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-full focus:outline-none focus:ring-2 focus:ring-primary">
                        <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                    </div>
                </div>

                <a href="index.html" class="block px-3 py-3 text-gray-700 hover:text-primary hover:bg-purple-light rounded-lg transition duration-300 font-medium">Home</a>

                <div class="space-y-2">
                    <button class="w-full flex items-center justify-between px-3 py-3 text-gray-700 hover:text-primary hover:bg-purple-light rounded-lg transition duration-300 font-medium" onclick="toggleMobileSubmenu('women')">
                        Women <i class="fas fa-chevron-down transition-transform duration-300" id="women-arrow"></i>
                    </button>
                    <div id="women-submenu" class="hidden pl-6 space-y-1">
                        <a href="products.html?category=dresses" class="block px-3 py-2 text-sm text-gray-600 hover:text-primary">Dresses</a>
                        <a href="products.html?category=tops" class="block px-3 py-2 text-sm text-gray-600 hover:text-primary">Tops</a>
                        <a href="products.html?category=bottoms" class="block px-3 py-2 text-sm text-gray-600 hover:text-primary">Bottoms</a>
                        <a href="products.html?category=shoes&gender=women" class="block px-3 py-2 text-sm text-gray-600 hover:text-primary">Women's Shoes</a>
                    </div>
                </div>

                <div class="space-y-2">
                    <button class="w-full flex items-center justify-between px-3 py-3 text-gray-700 hover:text-primary hover:bg-purple-light rounded-lg transition duration-300 font-medium" onclick="toggleMobileSubmenu('men')">
                        Men <i class="fas fa-chevron-down transition-transform duration-300" id="men-arrow"></i>
                    </button>
                    <div id="men-submenu" class="hidden pl-6 space-y-1">
                        <a href="products.html?category=shirts" class="block px-3 py-2 text-sm text-gray-600 hover:text-primary">Shirts</a>
                        <a href="products.html?category=pants" class="block px-3 py-2 text-sm text-gray-600 hover:text-primary">Pants</a>
                        <a href="products.html?category=jackets" class="block px-3 py-2 text-sm text-gray-600 hover:text-primary">Jackets</a>
                        <a href="products.html?category=shoes&gender=men" class="block px-3 py-2 text-sm text-gray-600 hover:text-primary">Men's Shoes</a>
                    </div>
                </div>

                <a href="#" class="block px-3 py-3 text-red-500 hover:text-red-600 hover:bg-red-50 rounded-lg transition duration-300 font-medium">Sale</a>

                <!-- Mobile Auth Buttons -->
                <div class="border-t border-gray-200 pt-4 mt-4 space-y-2">
                    <button onclick="openLoginModal()" class="w-full px-3 py-3 text-gray-700 hover:text-primary hover:bg-purple-light rounded-lg transition duration-300 font-medium text-left">
                        Login
                    </button>
                    <button onclick="openSignupModal()" class="w-full bg-primary text-white px-3 py-3 rounded-lg font-medium hover:bg-accent transition duration-300">
                        Sign Up
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Breadcrumb -->
    <section class="pt-24 pb-8 bg-gradient-to-b from-gray-50 to-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <nav class="flex items-center space-x-2 text-sm text-gray-600 mb-8">
                <a href="index.html" class="hover:text-primary transition duration-300">Home</a>
                <i class="fas fa-chevron-right text-xs"></i>
                <span id="breadcrumb-category" class="text-primary font-medium">Products</span>
            </nav>

            <!-- Category Header -->
            <div class="text-center mb-12">
                <h1 id="category-title" class="text-4xl lg:text-5xl font-bold text-gray-900 mb-4 font-display">All Products</h1>
                <p id="category-description" class="text-xl text-gray-600 max-w-2xl mx-auto">Discover our complete collection of premium fashion items</p>
            </div>
        </div>
    </section>

    <!-- Product Filters and Grid -->
    <section class="py-12 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Filter Controls -->
            <div class="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-8 space-y-4 lg:space-y-0">
                <div class="flex items-center space-x-4">
                    <span class="text-gray-600">Showing</span>
                    <span class="text-gray-600"><span id="product-count">0</span> products</span>
                </div>

                <div class="flex flex-col sm:flex-row items-start sm:items-center space-y-4 sm:space-y-0 sm:space-x-4">
                    <!-- Sort Dropdown -->
                    <div class="relative">
                        <select class="appearance-none bg-white/90 backdrop-blur-sm border border-gray-200 rounded-2xl px-4 py-3 pr-10 text-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-300 hover:bg-white hover:shadow-md cursor-pointer" id="sort-select">
                            <option value="featured">Featured</option>
                            <option value="price-low">Price: Low to High</option>
                            <option value="price-high">Price: High to Low</option>
                            <option value="newest">Newest First</option>
                            <option value="rating">Highest Rated</option>
                        </select>
                        <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                            <i class="fas fa-chevron-down text-gray-400 text-sm"></i>
                        </div>
                    </div>

                    <!-- Back to Categories Button -->
                    <a href="index.html#categories" class="bg-primary text-white px-6 py-3 rounded-2xl font-medium hover:bg-accent transition-all duration-300 transform hover:scale-105 flex items-center space-x-2">
                        <i class="fas fa-arrow-left"></i>
                        <span>Back to Categories</span>
                    </a>
                </div>
            </div>

            <!-- Products Grid -->
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8" id="products-grid">
                <!-- Products will be dynamically loaded here -->
            </div>

            <!-- Load More Button -->
            <div class="text-center mt-12">
                <button id="load-more-btn" class="bg-white border-2 border-primary text-primary px-8 py-4 rounded-2xl font-semibold hover:bg-primary hover:text-white transition-all duration-300 transform hover:scale-105">
                    Load More Products
                </button>
            </div>
        </div>
    </section>

    <!-- Login Modal -->
    <div id="login-modal" class="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div id="login-modal-content" class="bg-white rounded-2xl shadow-2xl max-w-md w-full p-8 transform scale-95 transition-transform duration-300">
                <div class="text-center mb-8">
                    <h2 class="text-3xl font-bold text-gray-900 font-display">Welcome Back</h2>
                    <p class="text-gray-600 mt-2">Sign in to your account</p>
                </div>

                <form id="login-form" class="space-y-6">
                    <div class="relative">
                        <input type="email" id="login-email" class="peer w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-300 placeholder-transparent" placeholder="Email" required>
                        <label for="login-email" class="absolute left-4 top-3 text-gray-500 transition-all duration-300 pointer-events-none">Email Address</label>
                    </div>

                    <div class="relative">
                        <input type="password" id="login-password" class="peer w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-300 placeholder-transparent" placeholder="Password" required>
                        <label for="login-password" class="absolute left-4 top-3 text-gray-500 transition-all duration-300 pointer-events-none">Password</label>
                        <button type="button" onclick="togglePasswordVisibility('login-password')" class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600">
                            <i id="login-password-icon" class="fas fa-eye"></i>
                        </button>
                    </div>

                    <div class="flex items-center justify-between">
                        <label class="flex items-center">
                            <input type="checkbox" class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                            <span class="ml-2 text-sm text-gray-600">Remember me</span>
                        </label>
                        <a href="#" class="text-sm text-primary hover:text-accent">Forgot password?</a>
                    </div>

                    <button type="submit" class="w-full bg-primary text-white py-3 rounded-lg font-semibold hover:bg-accent transition-all duration-300 transform hover:scale-105">
                        Sign In
                    </button>
                </form>

                <div class="mt-6 text-center">
                    <p class="text-gray-600">Don't have an account?
                        <button onclick="switchToSignup()" class="text-primary hover:text-accent font-medium">Sign up</button>
                    </p>
                </div>

                <button onclick="closeAuthModal()" class="absolute top-4 right-4 text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Signup Modal -->
    <div id="signup-modal" class="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div id="signup-modal-content" class="bg-white rounded-2xl shadow-2xl max-w-md w-full p-8 transform scale-95 transition-transform duration-300">
                <div class="text-center mb-8">
                    <h2 class="text-3xl font-bold text-gray-900 font-display">Join Luxe Fashion</h2>
                    <p class="text-gray-600 mt-2">Create your account today</p>
                </div>

                <form id="signup-form" class="space-y-6">
                    <div class="relative">
                        <input type="text" id="signup-name" class="peer w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-300 placeholder-transparent" placeholder="Full Name" required>
                        <label for="signup-name" class="absolute left-4 top-3 text-gray-500 transition-all duration-300 pointer-events-none">Full Name</label>
                    </div>

                    <div class="relative">
                        <input type="email" id="signup-email" class="peer w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-300 placeholder-transparent" placeholder="Email" required>
                        <label for="signup-email" class="absolute left-4 top-3 text-gray-500 transition-all duration-300 pointer-events-none">Email Address</label>
                        <div id="email-validation" class="hidden text-red-500 text-xs mt-1">Please enter a valid email address</div>
                    </div>

                    <div class="relative">
                        <input type="password" id="signup-password" class="peer w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-300 placeholder-transparent" placeholder="Password" required>
                        <label for="signup-password" class="absolute left-4 top-3 text-gray-500 transition-all duration-300 pointer-events-none">Password</label>
                        <button type="button" onclick="togglePasswordVisibility('signup-password')" class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600">
                            <i id="signup-password-icon" class="fas fa-eye"></i>
                        </button>
                    </div>

                    <div id="password-strength" class="hidden">
                        <div class="flex space-x-1 mb-2">
                            <div id="strength-1" class="h-1 flex-1 bg-gray-200 rounded"></div>
                            <div id="strength-2" class="h-1 flex-1 bg-gray-200 rounded"></div>
                            <div id="strength-3" class="h-1 flex-1 bg-gray-200 rounded"></div>
                            <div id="strength-4" class="h-1 flex-1 bg-gray-200 rounded"></div>
                        </div>
                        <p id="strength-text" class="text-xs text-gray-500">Password strength</p>
                    </div>

                    <div class="relative">
                        <input type="password" id="signup-confirm-password" class="peer w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary transition-all duration-300 placeholder-transparent" placeholder="Confirm Password" required>
                        <label for="signup-confirm-password" class="absolute left-4 top-3 text-gray-500 transition-all duration-300 pointer-events-none">Confirm Password</label>
                        <div id="password-match" class="hidden text-red-500 text-xs mt-1">Passwords do not match</div>
                    </div>

                    <label class="flex items-start">
                        <input type="checkbox" id="terms-checkbox" class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded mt-1" required>
                        <span class="ml-2 text-sm text-gray-600">I agree to the <a href="#" class="text-primary hover:text-accent">Terms of Service</a> and <a href="#" class="text-primary hover:text-accent">Privacy Policy</a></span>
                    </label>

                    <button type="submit" class="w-full bg-primary text-white py-3 rounded-lg font-semibold hover:bg-accent transition-all duration-300 transform hover:scale-105">
                        Create Account
                    </button>
                </form>

                <div class="mt-6 text-center">
                    <p class="text-gray-600">Already have an account?
                        <button onclick="switchToLogin()" class="text-primary hover:text-accent font-medium">Sign in</button>
                    </p>
                </div>

                <button onclick="closeAuthModal()" class="absolute top-4 right-4 text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="script.js"></script>
    <script src="products.js"></script>
</body>
</html>
